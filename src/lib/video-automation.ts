/**
 * Video Automation Types
 *
 * This file contains type definitions for video automation requests
 * used across different video creation workflows.
 */

export interface AutomationRequest {
  // Core content
  idea: string
  
  // Optional content sources
  blogUrl?: string
  pdfUrl?: string
  audioUrl?: string
  audioName?: string
  podcastUrl?: string
  episodeTitle?: string
  episodeDescription?: string
  episodeDuration?: number
  
  // Video configuration
  tone: string
  audience: string
  platform: string
  hook: boolean
  callToAction: boolean
  keywords?: string
  duration: number
  language: string
  orientation: 'landscape' | 'portrait' | 'square'
  autopick: string
  clipPace?: 'fast' | 'medium' | 'slow' | 'verySlow'
  
  // Voice settings
  voice: {
    voice_id: string
    name: string
  } | null
  
  // User and method
  userId?: string
  method: string
}
