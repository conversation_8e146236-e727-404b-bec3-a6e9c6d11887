import { createClient } from '@supabase/supabase-js'
import {
  getFunctions,
  renderMediaOnLambda,
  getRenderProgress,
} from '@remotion/lambda/client'
import {
  getServices as getCloudRunServices,
  renderMediaOnCloudrun,
} from '@remotion/cloudrun/client'

// Top-level supabase client for all helpers
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Helper functions
export async function updateRenderStatus(
  renderId: string,
  status: string,
  progress: number,
  publicUrl?: string,
  errorMessage?: string
) {
  // Use Supabase client instead of Dr<PERSON><PERSON> to trigger real-time events
  const { error } = await supabase
    .from('render_jobs')
    .update({
      status,
      progress,
      public_url: publicUrl,
      error_message: errorMessage,
      updated_at: new Date().toISOString(),
    })
    .eq('id', renderId)

  if (error) {
    console.error('Error updating render status:', error)
    throw error
  }
}

// Polling with milestone updates
function shouldUpdateMilestone(lastMilestone: number, progress: number) {
  const milestones = [10, 25, 50, 100]
  return milestones.some(m => lastMilestone < m && progress >= m)
}

export async function renderWithLambda(
  inputProps: Record<string, unknown>,
  renderId: string,
  userId: string,
  projectId: string
): Promise<{ publicUrl: string }> {
  const functions = await getFunctions({
    region: 'us-east-1',
    compatibleOnly: true,
  })

  const outputPath = `renders/${userId}/${renderId}.mp4`
  const { bucketName, renderId: lambdaRenderId } = await renderMediaOnLambda({
    serveUrl: process.env.REMOTION_AWS_SERVE_URL!,
    functionName: functions[0].functionName,
    composition: 'main',
    region: 'us-east-1',
    codec: 'h264',
    inputProps,
    outName: {
      bucketName: process.env.REMOTION_AWS_S3_BUCKET_NAME!,
      key: outputPath,
    },
  })
  // Poll for progress
  let progress = await getRenderProgress({
    renderId: lambdaRenderId,
    bucketName,
    functionName: functions[0].functionName,
    region: 'us-east-1',
  })
  let lastMilestone = 0
  while (progress.done === false) {
    const percent = Math.round(progress.overallProgress * 100)
    if (
      shouldUpdateMilestone(lastMilestone, percent) ||
      percent - lastMilestone >= 2
    ) {
      await updateRenderStatus(renderId, 'rendering', percent)
      lastMilestone = percent
    }
    await new Promise(resolve => setTimeout(resolve, 2000))
    progress = await getRenderProgress({
      renderId: lambdaRenderId,
      bucketName,
      functionName: functions[0].functionName,
      region: 'us-east-1',
    })
  }
  return {
    publicUrl: `${process.env.REMOTION_AWS_S3_PUBLIC_URL}/${outputPath}`,
  }
}

export async function renderWithCloudRun(
  inputProps: Record<string, unknown>,
  renderId: string,
  userId: string,
  projectId: string
): Promise<{ publicUrl: string }> {
  // Get available Cloud Run services
  const services = await getCloudRunServices({
    region: 'us-east1',
    compatibleOnly: true,
  })
  if (!services.length) {
    throw new Error('No compatible Cloud Run services found in us-east1')
  }
  const serviceName = services[0].serviceName

  // Progress callback
  let lastMilestone = 0
  const updateRenderProgress = (progress: number, error: boolean) => {
    const percent = Math.round(progress * 100)
    if (error) {
      updateRenderStatus(renderId, 'failed', percent)
    } else if (
      shouldUpdateMilestone(lastMilestone, percent) ||
      percent - lastMilestone >= 2
    ) {
      updateRenderStatus(renderId, 'rendering', percent)
      lastMilestone = percent
    }
  }

  const outputPath = `${userId}/${renderId}.mp4`

  // Start the render
  const result = await renderMediaOnCloudrun({
    serviceName,
    region: 'us-east1',
    serveUrl: process.env.REMOTION_GCP_SERVE_URL!,
    composition: 'main',
    inputProps,
    codec: 'h264',
    outName: `${renderId}.mp4`,
    updateRenderProgress,
    renderIdOverride: userId,
  })

  if (result.type === 'success') {
    return {
      publicUrl: `${process.env.REMOTION_GCP_S3_PUBLIC_URL}/${outputPath}`,
    }
  } else {
    throw new Error(result.message || 'CloudRun render failed')
  }
}
