'use client'

import { useEffect, useState } from 'react'
import { useUser } from '@clerk/nextjs'
import { supabase } from '@/lib/supabase-client'

export interface RenderJob {
  id: string
  projectId: string
  userId: string
  status: 'initializing' | 'rendering' | 'completed' | 'failed'
  progress: number
  publicUrl?: string
  thumbnailUrl?: string
  errorMessage?: string
  renderMethod?: string
  exportName?: string
  exportResolution?: string
  createdAt: Date
  updatedAt: Date
  youtubeId?: string
}

export function useRenderJobs(projectId?: string) {
  const { user } = useUser()
  const [renderJobs, setRenderJobs] = useState<RenderJob[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [connectionStatus, setConnectionStatus] =
    useState<string>('disconnected')
  const [updateCounter, setUpdateCounter] = useState(0)

  useEffect(() => {
    if (!user?.id) return

    console.log('🔧 useRenderJobs effect running with:', {
      userId: user.id,
      projectId,
    })

    const fetchRenderJobs = async () => {
      try {
        let query = supabase
          .from('render_jobs')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })

        if (projectId) {
          query = query.eq('project_id', projectId)
        }

        const { data, error } = await query

        if (error) throw error
        // Normalize DB fields to camelCase for UI
        const normalized = (data || []).map((job: any) => ({
          ...job,
          createdAt: job.created_at ? new Date(job.created_at) : undefined,
          updatedAt: job.updated_at ? new Date(job.updated_at) : undefined,
          thumbnailUrl: job.thumbnail_url,
          publicUrl: job.public_url,
          youtubeId: job.youtube_id,
          errorMessage: job.error_message,
          exportName: job.export_name,
          exportResolution: job.export_resolution,
        }))
        setRenderJobs(normalized)
      } catch (error) {
        console.error('Error fetching render jobs:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchRenderJobs()

    // Set up real-time subscription
    const channel = supabase
      .channel('render_jobs_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'render_jobs',
          filter: `user_id=eq.${user.id}`,
        },
        payload => {
          console.log('🔄 Real-time update received:', {
            eventType: payload.eventType,
            table: payload.table,
            schema: payload.schema,
            new: payload.new,
            old: payload.old,
          })

          // Log the raw payload.new to see exact field names
          if (payload.new) {
            console.log('📋 Raw payload.new fields:', Object.keys(payload.new))
            console.log('📋 Raw payload.new data:', payload.new)
          }

          // Normalize the payload data to match our RenderJob interface
          const normalizeJob = (job: any): RenderJob => ({
            ...job,
            projectId: job.project_id, // Map snake_case to camelCase
            userId: job.user_id, // Map snake_case to camelCase
            createdAt: job.created_at ? new Date(job.created_at) : undefined,
            updatedAt: job.updated_at ? new Date(job.updated_at) : undefined,
            thumbnailUrl: job.thumbnail_url,
            publicUrl: job.public_url,
            youtubeId: job.youtube_id,
            errorMessage: job.error_message,
            exportName: job.export_name,
            exportResolution: job.export_resolution,
          })

          if (payload.eventType === 'INSERT') {
            const newJob = normalizeJob(payload.new)
            console.log('📝 INSERT: Adding new job:', newJob.id, newJob.status)
            // Only add if it matches the project filter (if any)
            if (!projectId || newJob.projectId === projectId) {
              setRenderJobs(prev => {
                const updated = [newJob, ...prev]
                console.log(
                  '📝 State updated, job count:',
                  updated.length,
                  'new job:',
                  newJob
                )
                setUpdateCounter(c => c + 1) // Force re-render
                return updated
              })
            } else {
              console.log(
                '🚫 New job filtered out due to project mismatch:',
                newJob.projectId,
                'vs',
                projectId
              )
            }
          } else if (payload.eventType === 'UPDATE') {
            const updatedJob = normalizeJob(payload.new)
            console.log(
              '🔄 UPDATE: Updating job:',
              updatedJob.id,
              'status:',
              updatedJob.status,
              'progress:',
              updatedJob.progress
            )
            // Only update if it matches the project filter (if any)
            if (!projectId || updatedJob.projectId === projectId) {
              setRenderJobs(prev => {
                const updated = prev.map(job =>
                  job.id === updatedJob.id ? updatedJob : job
                )
                console.log(
                  '🔄 State updated, job count:',
                  updated.length,
                  'updated job:',
                  updatedJob
                )
                setUpdateCounter(c => c + 1) // Force re-render
                return updated
              })
            } else {
              console.log(
                '🚫 Job filtered out due to project mismatch:',
                updatedJob.projectId,
                'vs',
                projectId
              )
            }
          } else if (payload.eventType === 'DELETE') {
            console.log('🗑️ DELETE: Removing job:', payload.old.id)
            setRenderJobs(prev => {
              const updated = prev.filter(job => job.id !== payload.old.id)
              setUpdateCounter(c => c + 1) // Force re-render
              return updated
            })
          }
        }
      )
      .subscribe(status => {
        console.log('Supabase subscription status:', status)
        setConnectionStatus(status)
        if (status === 'SUBSCRIBED') {
          console.log(
            '✅ Successfully subscribed to render_jobs changes for user:',
            user.id
          )
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Error subscribing to render_jobs changes')
          // Try to reconnect after a short delay
          setTimeout(() => {
            console.log('🔄 Attempting to reconnect...')
            channel.unsubscribe()
            // The useEffect will re-run and create a new subscription
          }, 2000)
        } else if (status === 'TIMED_OUT') {
          console.error('⏰ Subscription to render_jobs timed out')
        } else if (status === 'CLOSED') {
          console.log('🔒 Subscription to render_jobs closed')
        }
      })

    return () => {
      channel.unsubscribe()
    }
  }, [user?.id, projectId])

  // Add a manual refresh function
  const refreshJobs = async () => {
    if (!user?.id) return
    setIsLoading(true)
    try {
      let query = supabase
        .from('render_jobs')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (projectId) {
        query = query.eq('project_id', projectId)
      }

      const { data, error } = await query

      if (error) throw error
      // Normalize DB fields to camelCase for UI
      const normalized = (data || []).map((job: any) => ({
        ...job,
        createdAt: job.created_at ? new Date(job.created_at) : undefined,
        updatedAt: job.updated_at ? new Date(job.updated_at) : undefined,
        thumbnailUrl: job.thumbnail_url,
        publicUrl: job.public_url,
        youtubeId: job.youtube_id,
        errorMessage: job.error_message,
        exportName: job.export_name,
        exportResolution: job.export_resolution,
      }))
      setRenderJobs(normalized)
    } catch (error) {
      console.error('Error refreshing render jobs:', error)
    } finally {
      setIsLoading(false)
    }
  }
  return { renderJobs, isLoading, refreshJobs, connectionStatus, updateCounter }
}

// Paginated render jobs for /my-videos

export function usePaginatedRenderJobs(page = 1, limit = 8) {
  const { user } = useUser()
  const [jobs, setJobs] = useState<RenderJob[]>([])
  const [total, setTotal] = useState(0)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!user?.id) return
    setIsLoading(true)
    const fetchJobs = async () => {
      // Get total count
      const { count } = await supabase
        .from('render_jobs')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)
      setTotal(count || 0)
      // Get paginated jobs
      const from = (page - 1) * limit
      const to = from + limit - 1
      const { data, error } = await supabase
        .from('render_jobs')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .range(from, to)
      if (error) {
        setJobs([])
      } else {
        const normalized = (data || []).map((job: any) => ({
          ...job,
          createdAt: job.created_at ? new Date(job.created_at) : undefined,
          updatedAt: job.updated_at ? new Date(job.updated_at) : undefined,
          thumbnailUrl: job.thumbnail_url,
          publicUrl: job.public_url,
          youtubeId: job.youtube_id,
          errorMessage: job.error_message,
          exportName: job.export_name,
          exportResolution: job.export_resolution,
        }))
        setJobs(normalized)
      }
      setIsLoading(false)
    }
    fetchJobs()
  }, [user?.id, page, limit])

  const refreshJobs = () => {
    setIsLoading(true)
    // Triggers useEffect
    setJobs([])
  }

  return { jobs, total, isLoading, refreshJobs }
}
