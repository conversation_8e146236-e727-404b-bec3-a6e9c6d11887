import { useMutation, useQueryClient } from '@tanstack/react-query'
import type { AutomationRequest } from '@/lib/video-automation'
import type { Scene } from '@/types/video'

export interface VideoAutomationResponse {
  success: boolean
  scenes: Scene[]
  totalScenes: number
  totalDuration: number
  processedScenes: number
  successfulMedia: number
  successfulVoiceovers: number
}

export function useVideoAutomation() {
  const queryClient = useQueryClient()

  return useMutation<VideoAutomationResponse, Error, AutomationRequest>({
    mutationFn: async (request: AutomationRequest) => {
      const response = await fetch('/api/video/automation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(
          errorData.details || errorData.error || 'Video automation failed'
        )
      }

      return response.json()
    },
    onSuccess: () => {
      // Invalidate projects cache when a new video is created
      queryClient.invalidateQueries({ queryKey: ['projects'] })
    },
  })
}
