import type { ElevenVoice } from '@/hooks/useElevenVoicesQuery'

// Base configuration interface for all video creation forms
export interface BaseVideoConfig {
  tone: string
  audience: string
  platform: string
  duration: number
  includeHook: boolean
  includeCTA: boolean
  language: string
  orientation: string
  autopick: string
  voice: ElevenVoice | null
  keywords: string
}

// Extended configurations for specific video types
export interface IdeaVideoConfig extends BaseVideoConfig {
  idea: string
  creativity: number
  blogUrl?: string
}

export interface BlogVideoConfig extends BaseVideoConfig {
  blogUrl: string
}

export interface TextVideoConfig extends BaseVideoConfig {
  text: string
  creativity: number
}

export interface PDFVideoConfig extends BaseVideoConfig {
  pdfUrl?: string
  files?: File[] // For file upload state
}

// Common form field options
export const TONE_OPTIONS = [
  { value: 'casual', label: '😊 Casual' },
  { value: 'formal', label: '🎩 Formal' },
  { value: 'friendly', label: '😄 Friendly' },
  { value: 'professional', label: '💼 Professional' },
  { value: 'enthusiastic', label: '🎉 Enthusiastic' },
  { value: 'educational', label: '📚 Educational' },
] as const

export const AUDIENCE_OPTIONS = [
  { value: 'general', label: '👥 General Audience' },
  { value: 'business', label: '💼 Business Professionals' },
  { value: 'students', label: '🎓 Students' },
  { value: 'tech', label: '💻 Tech-Savvy Users' },
  { value: 'creatives', label: '🎨 Creative Professionals' },
  { value: 'entrepreneurs', label: '🚀 Entrepreneurs' },
] as const

export const PLATFORM_OPTIONS = [
  { value: 'youtube', label: '📺 YouTube' },
  { value: 'tiktok', label: '🎵 TikTok Clip' },
  { value: 'instagram', label: '📸 Instagram Reel' },
  { value: 'linkedin', label: '💼 LinkedIn Post' },
  { value: 'twitter', label: '🐦 Twitter/X' },
] as const

export const LANGUAGE_OPTIONS = [
  { value: 'english', label: '🇺🇸 English' },
  { value: 'spanish', label: '🇪🇸 Spanish' },
  { value: 'french', label: '🇫🇷 French' },
  { value: 'german', label: '🇩🇪 German' },
  { value: 'italian', label: '🇮🇹 Italian' },
  { value: 'portuguese', label: '🇵🇹 Portuguese' },
  { value: 'dutch', label: '🇳🇱 Dutch' },
  { value: 'russian', label: '🇷🇺 Russian' },
  { value: 'japanese', label: '🇯🇵 Japanese' },
  { value: 'korean', label: '🇰🇷 Korean' },
  { value: 'chinese', label: '🇨🇳 Chinese' },
  { value: 'arabic', label: '🇸🇦 Arabic' },
  { value: 'hindi', label: '🇮🇳 Hindi' },
  { value: 'turkish', label: '🇹🇷 Turkish' },
  { value: 'polish', label: '🇵🇱 Polish' },
  { value: 'ukrainian', label: '🇺🇦 Ukrainian' },
  { value: 'vietnamese', label: '🇻🇳 Vietnamese' },
  { value: 'swedish', label: '🇸🇪 Swedish' },
  { value: 'norwegian', label: '🇳🇴 Norwegian' },
  { value: 'danish', label: '🇩🇰 Danish' },
  { value: 'finnish', label: '🇫🇮 Finnish' },
  { value: 'czech', label: '🇨🇿 Czech' },
  { value: 'hungarian', label: '🇭🇺 Hungarian' },
  { value: 'romanian', label: '🇷🇴 Romanian' },
  { value: 'bulgarian', label: '🇧🇬 Bulgarian' },
  { value: 'croatian', label: '🇭🇷 Croatian' },
  { value: 'slovak', label: '🇸🇰 Slovak' },
  { value: 'greek', label: '🇬🇷 Greek' },
  { value: 'indonesian', label: '🇮🇩 Indonesian' },
  { value: 'malay', label: '🇲🇾 Malay' },
  { value: 'tamil', label: '🇱🇰 Tamil' },
  { value: 'filipino', label: '🇵🇭 Filipino' },
] as const

export const ORIENTATION_OPTIONS = [
  { value: 'landscape', label: '📱 Landscape' },
  { value: 'portrait', label: '📲 Portrait' },
  { value: 'square', label: '⬜ Square' },
] as const

export const AUTOPICK_OPTIONS = [
  { value: 'stock-videos', label: '🎬 Stock Videos' },
  { value: 'stock-images', label: '📷 Stock Images' },
  { value: 'ai-images', label: '🤖 AI Generated Images' },
  { value: 'mix', label: '🎭 Mix of All' },
] as const
