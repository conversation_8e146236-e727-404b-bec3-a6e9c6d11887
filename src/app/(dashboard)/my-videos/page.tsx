'use client'
import { useEffect, useState } from 'react'
import { VideoCard } from './_components/VideoCard'
import { usePaginatedRenderJobs } from '@/hooks/useRenderJobs'

export default function MyVideosPage() {
  const [page, setPage] = useState(1)
  const limit = 8
  const {
    jobs: paginatedJobs,
    total,
    isLoading,
  } = usePaginatedRenderJobs(page, limit)
  const [jobs, setJobs] = useState(paginatedJobs)

  useEffect(() => {
    setJobs(paginatedJobs)
  }, [paginatedJobs])

  // Pagination helpers
  const totalPages = Math.ceil(total / limit)
  const startIdx = total === 0 ? 0 : (page - 1) * limit + 1
  const endIdx = Math.min(page * limit, total)

  // Pagination controls
  const pages = []
  for (let i = 1; i <= totalPages; i++) {
    if (i === 1 || i === totalPages || Math.abs(i - page) <= 1) {
      pages.push(i)
    } else if (pages[pages.length - 1] !== '...') {
      pages.push('...')
    }
  }

  return (
    <div className='container mx-auto p-6'>
      <div className='flex items-center justify-between mb-6'>
        <h1 className='text-3xl font-bold'>My Videos</h1>
        <div className='text-muted-foreground text-sm'>
          {isLoading || !total
            ? ''
            : `${startIdx}-${endIdx} of ${total} videos`}
        </div>
      </div>
      {isLoading ? (
        <div className='bg-card p-8 rounded-lg text-center'>Loading...</div>
      ) : jobs.length === 0 ? (
        <div className='bg-card p-8 rounded-lg text-center'>
          <p className='text-muted-foreground'>
            You don&apos;t have any videos yet.
          </p>
        </div>
      ) : (
        <div className='grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6'>
          {jobs.map((job, idx) => (
            <VideoCard
              key={job.id || idx}
              thumbnail={job.thumbnailUrl || null}
              created_at={job.createdAt}
              url={job.publicUrl || null}
              youtubeId={job.youtubeId || null}
              onPublish={() => {}}
              status={job.status}
              progress={job.progress}
              errorMessage={job.errorMessage}
              exportName={job.exportName}
              exportResolution={job.exportResolution}
              renderJobId={job.id}
              onDeleted={() =>
                setJobs(jobs => jobs.filter(j => j.id !== job.id))
              }
            />
          ))}
        </div>
      )}
      {/* Pagination controls */}
      {totalPages > 1 && (
        <div className='flex justify-center items-center gap-2 mt-8'>
          <button
            className='px-3 py-1 rounded disabled:opacity-50'
            onClick={() => setPage(page - 1)}
            disabled={page === 1}
          >
            Previous
          </button>
          {pages.map((p, i) =>
            p === '...' ? (
              <span key={i} className='px-2'>
                …
              </span>
            ) : (
              <button
                key={p}
                className={`px-3 py-1 rounded ${p === page ? 'bg-primary text-white' : ''}`}
                onClick={() => setPage(Number(p))}
                disabled={p === page}
              >
                {p}
              </button>
            )
          )}
          <button
            className='px-3 py-1 rounded disabled:opacity-50'
            onClick={() => setPage(page + 1)}
            disabled={page === totalPages}
          >
            Next
          </button>
        </div>
      )}
    </div>
  )
}
