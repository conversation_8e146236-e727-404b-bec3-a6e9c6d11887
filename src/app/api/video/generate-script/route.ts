import { NextRequest, NextResponse } from 'next/server'
import {
  generateVideoScript,
  type ScriptGenerationRequest,
} from '@/lib/video-automation'
import { createErrorResponse } from '@/lib/api-cache'

export async function POST(req: NextRequest) {
  try {
    const body: ScriptGenerationRequest = await req.json()

    if (!body.idea) {
      return createErrorResponse('Video idea is required', 400)
    }

    const script = await generateVideoScript(body)

    return NextResponse.json({
      success: true,
      script,
    })
  } catch (error) {
    console.error('❌ Script generation error:', error)
    return createErrorResponse('Failed to generate script', 500)
  }
}
